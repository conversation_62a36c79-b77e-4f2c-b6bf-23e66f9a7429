#!/bin/bash

# Mareo Agent API 测试脚本
# 使用方法: bash test_api.sh

echo "🧪 测试 Mareo Agent API..."

# 1. 检查服务器是否运行
echo "1️⃣ 检查服务器状态..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问"
    echo "请先运行: bash start_server.sh"
    exit 1
fi

# 2. 测试根路径重定向
echo ""
echo "2️⃣ 测试根路径重定向..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/)
if [ "$response" = "307" ] || [ "$response" = "200" ]; then
    echo "✅ 根路径重定向正常 (HTTP $response)"
else
    echo "❌ 根路径重定向异常 (HTTP $response)"
fi

# 3. 测试 Web UI 访问
echo ""
echo "3️⃣ 测试 Web UI 访问..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/dev-ui/)
if [ "$response" = "200" ]; then
    echo "✅ Web UI 访问正常 (HTTP $response)"
else
    echo "❌ Web UI 访问异常 (HTTP $response)"
fi

# 4. 测试 API 文档
echo ""
echo "4️⃣ 测试 API 文档..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/docs)
if [ "$response" = "200" ]; then
    echo "✅ API 文档访问正常 (HTTP $response)"
else
    echo "❌ API 文档访问异常 (HTTP $response)"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 可用的访问地址:"
echo "   - 主页: http://localhost:3000/"
echo "   - Web UI: http://localhost:3000/dev-ui/"
echo "   - API 文档: http://localhost:3000/docs"
echo "   - PDF 前端: http://localhost:3000/pdf-frontend/"
