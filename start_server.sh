#!/bin/bash

# <PERSON><PERSON> Agent 启动脚本
# 使用方法: bash start_server.sh

echo "🚀 启动 Mareo Agent 服务器..."

# 1. 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "❌ 错误: .env 文件不存在"
    echo "请先配置环境变量文件 .env，参考 README.md 中的要求"
    exit 1
fi

# 2. 加载环境变量
export $(grep -v '^#' .env | xargs)

# 3. 检查必需的环境变量
required_vars=("LANGFUSE_HOST" "LANGFUSE_PUBLIC_KEY" "LANGFUSE_SECRET_KEY" "OPENROUTER_API_KEY" "TAVILY_KEY" "TAVILY_BASE")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ] || [[ "${!var}" == *"your_"* ]]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ 错误: 以下环境变量未正确配置:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo ""
    echo "请在 .env 文件中设置这些变量的实际值"
    exit 1
fi

# 4. 启动服务器
echo "✅ 环境变量检查通过"
echo "🌐 启动服务器在 http://0.0.0.0:3000"
echo "📱 Web UI 访问地址: http://localhost:3000/dev-ui/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

poetry run uvicorn mareo.app.server:app --host 0.0.0.0 --port 3000 --reload
