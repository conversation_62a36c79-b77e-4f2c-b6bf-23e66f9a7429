[project]
name = "mareo"
readme = "README.md"
requires-python = ">=3.13,<4.0"

[tool.poetry]
package-mode = false

[tool.poetry.dependencies]
google-adk = { extras = ["eval"], version = ">=1.10.0" }
fastapi = "^0.116.1"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
langfuse = "^3.2.1"
watchfiles = "^1.1.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.12.5"
pre-commit = "^4.2.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.4.1"
pytest-asyncio = "^1.1.0"
pytest-mock = "^3.14.1"
pytest-xdist = "^3.8.0"
a2a-sdk = "^0.2.16"
langgraph = "^0.6.0"
coverage = "^7.10.2"
pytest-cov = "^6.2.1"

