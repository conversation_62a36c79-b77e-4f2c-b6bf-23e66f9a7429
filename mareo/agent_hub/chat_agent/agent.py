import os

from google.adk.agents import LlmAgent, SequentialAgent

from mareo.agent_hub.follow_up_agent.follow_up_agent import create_follow_up_agent
from mareo.agent_hub.html_artifact_agent import create_html_artifact_agent
from mareo.agent_hub.search_agent.agent import create_search_agent
from mareo.common.llm.predefined.qwen3_235b import qwen3_235b
from mareo.common.prompts.langfuse_provider import langfuse_prompt


def create_chat_agent() -> LlmAgent:
    """Factory method to create a new chat agent instance."""
    return LlmAgent(
        name='<PERSON><PERSON>',
        model=qwen3_235b,
        description='Agent to chat with user',
        instruction=langfuse_prompt.get_provider(
            prompt_name='Next-chat', label=os.getenv('LANGFUSE_PROMPT_LABEL', 'production')
        ),
        # 'You have specialized sub-agents: '
        # "1. 'Mareo_search': Handles online searching. Delegate to it for these. "
        # "Analyze the user's query. If it might require searching, delegate to 'Mareo_search'. Then respond with medium length content with the information returned. "
        # "If it's just chat, handle it yourself. "
        # 'For anything else, respond appropriately or state you cannot handle it.'
        # planner=MareoPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
        sub_agents=[create_search_agent(), create_html_artifact_agent()],
    )


def create_follow_up_flow_agent() -> SequentialAgent:
    """Factory method to create a sequential agent with chat and follow-up functionality."""
    return SequentialAgent(name='follow_up_flow', sub_agents=[create_chat_agent(), create_follow_up_agent()])


# Create agent with tools - tools will be created in proper async context when needed
chat_agent = create_chat_agent()

# Create the sequential agent combining chat and follow-up functionality
root_agent = create_follow_up_flow_agent()
